/* Member Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Avatar Styles */
.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.avatar-circle-lg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Profile Image Styles */
.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

.profile-image-lg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Status Badge */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.status-active {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-expired {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-frozen {
  background-color: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.2);
}

/* Button Group */
.btn-modern-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-modern-outline:hover, .btn-modern-outline.active {
  background-color: var(--primary-color);
  color: white;
}

.btn-group {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
}

.btn-group .btn-modern-outline {
  border-radius: 0;
  margin: 0;
  border-right-width: 0;
}

.btn-group .btn-modern-outline:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn-modern-outline:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-right-width: 1px;
}

/* Card View Styles */
.modern-card {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: var(--card-bg-color);
}

.modern-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modern-card .card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card .card-body {
  padding: 1.5rem;
}

/* Stat Cards */
.stat-card {
  border-radius: 0.75rem;
  padding: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  height: 100%;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0;
}

.stat-icon {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 3rem;
  opacity: 0.2;
}

/* Gradient Backgrounds */
.primary-gradient {
  background: linear-gradient(135deg, #4361ee, #3f37c9);
}

.success-gradient {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.warning-gradient {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.info-gradient {
  background: linear-gradient(135deg, #17a2b8, #4895ef);
}

.danger-gradient {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

/* Search Input */
.search-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  outline: none;
}

/* Total Members Badge */
.total-members-badge {
  display: flex;
  align-items: center;
}

.total-members-badge .modern-badge {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 50rem;
  display: inline-flex;
  align-items: center;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  transition: all 0.3s ease;
}

.total-members-badge .modern-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.total-members-badge .modern-badge i {
  font-size: 0.875rem;
}

/* Filter Tags */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  padding: 0.35rem 0.75rem;
  border-radius: 50rem;
  font-size: 0.875rem;
}

.remove-tag {
  margin-left: 0.5rem;
  font-size: 1.25rem;
  line-height: 1;
  cursor: pointer;
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
}

.modern-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-muted);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Button Styles */
.btn-modern {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-modern-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-modern-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-modern-primary:hover {
  background-color: var(--secondary-color);
}

.btn-modern-success {
  background-color: #28a745;
  color: white;
}

.btn-modern-success:hover {
  background-color: #218838;
}

.btn-modern-danger {
  background-color: #dc3545;
  color: white;
}

.btn-modern-danger:hover {
  background-color: #c82333;
}

.btn-modern-info {
  background-color: #17a2b8;
  color: white;
}

.btn-modern-info:hover {
  background-color: #138496;
}

.btn-modern-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.btn-modern-icon-sm {
  width: 30px;
  height: 30px;
  font-size: 0.875rem;
}

/* Staggered Animation */
.staggered-item {
  opacity: 0;
  transform: translateY(10px);
}

.staggered-item { animation: fadeIn 0.5s ease forwards; }


@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Pagination */
.modern-pagination {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.page-item {
  margin: 0 0.25rem;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--primary-color);
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.page-link:hover {
  z-index: 2;
  color: white;
  text-decoration: none;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.page-item.disabled .page-link {
  color: var(--text-muted);
  pointer-events: none;
  cursor: auto;
  background-color: var(--card-bg-color);
  border-color: var(--border-color);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }

  .avatar-circle-lg {
    width: 70px;
    height: 70px;
    font-size: 22px;
  }

  .modern-card .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .modern-card .card-header .d-flex {
    margin-top: 1rem;
    width: 100%;
    flex-direction: column;
    gap: 1rem;
  }

  .total-members-badge {
    order: -1;
    margin-bottom: 0.5rem;
  }

  .total-members-badge .modern-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .btn-modern-sm {
    width: 100%;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 2rem;
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(4px);
  pointer-events: none;
}

/* Empty State */
.text-center.py-5 {
  padding: 3rem 1rem;
  background-color: var(--card-bg-color);
  border-radius: 8px;
}

.text-center.py-5 i {
  opacity: 0.6;
  margin-bottom: 1rem;
}

/* Dark Mode Support */
[data-theme="dark"] .modern-card {
  background-color: #2d3748;
}

[data-theme="dark"] .modern-card .card-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .total-members-badge .modern-badge {
  background-color: var(--primary-light);
  color: var(--primary);
  border-color: rgba(var(--primary-rgb), 0.3);
}

[data-theme="dark"] .total-members-badge .modern-badge:hover {
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

[data-theme="dark"] .modern-table th {
  background-color: #4a5568;
  color: #e2e8f0;
  border-bottom-color: #2d3748;
}

[data-theme="dark"] .modern-table td {
  border-bottom-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .search-input {
  background-color: #4a5568;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .search-input:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
}

[data-theme="dark"] .page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .page-link:hover {
  background-color: #4a5568;
}

[data-theme="dark"] .page-item.disabled .page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #718096;
}

/* Dark Mode Profile Image Support */
[data-theme="dark"] .profile-image,
[data-theme="dark"] .profile-image-lg {
  border: 2px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .avatar-circle,
[data-theme="dark"] .avatar-circle-lg {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Profile Image Error Handling */
.avatar-circle.image-error img,
.avatar-circle-lg.image-error img {
  display: none !important;
}

.avatar-circle.image-error,
.avatar-circle-lg.image-error {
  /* Varsayılan ikon gösterilsin */
}

/* Profil resmi yüklenirken loading state */
.profile-image-loading {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

/* Gender filter disabled state */
.btn-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.remove-tag.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector .form-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  white-space: nowrap;
  margin-bottom: 0;
}

.page-size-selector select {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: var(--border-radius-sm);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  min-width: 70px;
}

.page-size-selector select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: 0;
}

.page-size-selector select option {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Dark mode için pagination dropdown */
[data-theme="dark"] .page-size-selector select {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .page-size-selector select option {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .member-card {
    margin-bottom: 1rem;
  }

  .member-info h5 {
    font-size: 1rem;
  }

  .member-info p {
    font-size: 0.8rem;
  }

  .btn-modern {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }

  .avatar-circle-lg {
    width: 60px;
    height: 60px;
    font-size: 18px;
  }

  .search-container {
    margin-bottom: 1rem;
  }

  .filter-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-buttons .btn-modern {
    width: 100%;
  }

  /* Mobile pagination */
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-controls {
    justify-content: center;
    gap: 1rem;
  }

  .pagination-info {
    text-align: center;
  }
}
