import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ListResponseModel } from '../models/listResponseModel';
import { Member } from '../models/member';

// Member interface'ini export et
export { Member } from '../models/member';
import { MemberBirthday } from '../models/member-birthday.model';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';
import { MemberQRInfoResponse } from '../models/member-qr-info.model';
import { PaginatedResult } from '../models/pagination'; // Eski modele geri dönüldü
// import { MemberPaginatedResponse } from '../models/memberPaginatedResponse'; // Bu import kaldırıldı
import { MemberFilter } from '../models/memberFilter';
import { SingleResponseModel } from '../models/singleResponseModel';
import { MemberDetailWithHistory } from '../models/member-detail-with-history.model';

@Injectable({
  providedIn: 'root',
})
export class MemberService extends BaseApiService {

  constructor(private httpClient:HttpClient) {
    super();
  }

  // getMemberStatistics metodu kaldırıldı

  // gender parametresi eklendi (opsiyonel, null olabilir)
  // pageSize parametresi eklendi
  getAllPaginated(pageNumber: number = 1, pageSize: number = 10, searchText: string = '', gender: number | null = null): Observable<SingleResponseModel<PaginatedResult<Member>>> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
      .set('searchText', searchText);

    // Eğer gender null veya undefined değilse, parametre olarak ekle
    if (gender !== null && gender !== undefined) {
      params = params.set('gender', gender.toString());
    }

    return this.httpClient.get<SingleResponseModel<PaginatedResult<Member>>>(
      `${this.apiUrl}member/getallpaginated`,
      { params }
    );
  }

  getMemberDetailsPaginated(
    pageNumber: number = 1,
    pageSize: number = 10,
    searchText?: string,
    gender?: number,
    branch?: string
  ): Observable<SingleResponseModel<PaginatedResult<MemberFilter>>> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString());

    if (searchText) params = params.set('searchText', searchText);
    if (gender !== undefined) params = params.set('gender', gender.toString());
    if (branch) params = params.set('branch', branch);

    return this.httpClient.get<SingleResponseModel<PaginatedResult<MemberFilter>>>(
      `${this.apiUrl}member/getmemberdetailspaginated`,
      { params }
    );
  }

  getMembers(): Observable<ListResponseModel<Member>> {
    let newPath = this.apiUrl + 'member/getactivemembers';

    return this.httpClient.get<ListResponseModel<Member>>(newPath);
  }

  getActiveMembers(): Observable<ListResponseModel<Member>> {
    return this.httpClient.get<ListResponseModel<Member>>(
      `${this.apiUrl}member/getactivemembers`
    );
  }
  add(member: Member): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'member/add',
      member
    );
  }
  delete(memberId: number): Observable<ResponseModel> {
    let deletePath = `${this.apiUrl}member/delete/?id=${memberId}`;
    return this.httpClient.delete<ResponseModel>(deletePath);
  }
  update(member: Member): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'member/update',
      member
    );
  }
  getMemberQRInfo(phoneNumber: string): Observable<MemberQRInfoResponse> {
    let path = `${this.apiUrl}member/getbyphone?phoneNumber=${phoneNumber}`;
    return this.httpClient.get<MemberQRInfoResponse>(path);
  }

  getMemberQRByUserId(): Observable<MemberQRInfoResponse> {
    let path = `${this.apiUrl}member/getmemberqrbyuserid`;
    return this.httpClient.get<MemberQRInfoResponse>(path);
  }
  getTotalActiveMembers(): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
        `${this.apiUrl}member/gettotalactivemembers`
    );
  }

  getMemberActiveMemberships(memberId: number): Observable<SingleResponseModel<any[]>> {
    return this.httpClient.get<SingleResponseModel<any[]>>(
      `${this.apiUrl}membership/getmemberactivememberships/${memberId}`
    );
  }

  getMembersByMultiplePackages(
    membershipTypeIds: number[],
    pageNumber: number = 1,
    pageSize: number = 10,
    searchText: string = '',
    gender?: number
  ): Observable<SingleResponseModel<PaginatedResult<MemberFilter>>> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
      .set('searchText', searchText);

    // Array parametrelerini ekle
    membershipTypeIds.forEach(id => {
      params = params.append('membershipTypeIds', id.toString());
    });

    // Cinsiyet parametresi eklendi
    if (gender !== undefined) {
      params = params.set('gender', gender.toString());
    }

    return this.httpClient.get<SingleResponseModel<PaginatedResult<MemberFilter>>>(
      `${this.apiUrl}member/getmembersbymultiplepackages`,
      { params }
    );
  }

  getMembersWithBalancePaginated(
    pageNumber: number = 1,
    pageSize: number = 10,
    searchText: string = '',
    balanceFilter: string = 'all'
  ): Observable<SingleResponseModel<PaginatedResult<Member>>> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString())
      .set('searchText', searchText)
      .set('balanceFilter', balanceFilter);

    return this.httpClient.get<SingleResponseModel<PaginatedResult<Member>>>(
      `${this.apiUrl}member/getmemberswithbalancepaginated`,
      { params }
    );
  }

  getTotalRegisteredMembers(): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
      `${this.apiUrl}member/gettotalregisteredmembers`
    );
  }
getActiveMemberCounts(): Observable<SingleResponseModel<{male: number, female: number}>> {
  return this.httpClient.get<SingleResponseModel<{male: number, female: number}>>(
    `${this.apiUrl}member/getactivemembercounts`
  );
}
getBranchCounts(): Observable<SingleResponseModel<{ [key: string]: number }>> {
  return this.httpClient.get<SingleResponseModel<{ [key: string]: number }>>(
    `${this.apiUrl}member/getbranchcounts`
  );
}

getUpcomingBirthdays(days: number = 3): Observable<ListResponseModel<MemberBirthday>> {
  // Use the dedicated API endpoint for upcoming birthdays
  return this.httpClient.get<ListResponseModel<MemberBirthday>>(
    `${this.apiUrl}member/getupcomingbirthdays?days=${days}`
  );
}

getMemberDetailById(memberId: number): Observable<SingleResponseModel<MemberDetailWithHistory>> {
  return this.httpClient.get<SingleResponseModel<MemberDetailWithHistory>>(
    `${this.apiUrl}member/getmemberdetailbyid/${memberId}`
  );
}
}
